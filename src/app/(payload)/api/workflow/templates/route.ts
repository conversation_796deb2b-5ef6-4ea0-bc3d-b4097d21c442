import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/workflow/templates
 * Returns available workflow templates
 */
export async function GET(request: NextRequest) {
  try {
    // Return predefined templates that match the workflow system expectations
    const templates = [
      {
        id: 'blog-post-seo',
        name: 'SEO Blog Post',
        description: 'Complete SEO-optimized blog post generation with keyword research and agent consultation',
        category: 'blog',
        tags: ['seo', 'blog', 'content-marketing'],
        difficulty: 'easy',
        estimatedTime: 45,
        consultationEnabled: true,
        agentCount: 3,
        steps: [
          { id: 'topic-input', name: 'Topic Input', type: 'TEXT_INPUT' },
          { id: 'keyword-research', name: 'Keyword Research', type: 'AI_GENERATION' },
          { id: 'content-strategy', name: 'Content Strategy', type: 'AI_GENERATION' },
          { id: 'content-generation', name: 'Content Generation', type: 'AI_GENERATION' },
          { id: 'seo-optimization', name: 'SEO Optimization', type: 'AI_GENERATION' },
          { id: 'human-review', name: 'Human Review', type: 'HUMAN_REVIEW' }
        ]
      },
      {
        id: 'product-page',
        name: 'Product Page',
        description: 'Comprehensive product page with features, benefits, and SEO optimization',
        category: 'ecommerce',
        tags: ['product', 'ecommerce', 'conversion'],
        difficulty: 'medium',
        estimatedTime: 60,
        consultationEnabled: true,
        agentCount: 4,
        steps: [
          { id: 'product-input', name: 'Product Details', type: 'TEXT_INPUT' },
          { id: 'market-research', name: 'Market Research', type: 'AI_GENERATION' },
          { id: 'content-strategy', name: 'Content Strategy', type: 'AI_GENERATION' },
          { id: 'content-generation', name: 'Content Generation', type: 'AI_GENERATION' },
          { id: 'seo-optimization', name: 'SEO Optimization', type: 'AI_GENERATION' },
          { id: 'human-review', name: 'Human Review', type: 'HUMAN_REVIEW' }
        ]
      },
      {
        id: 'buying-guide',
        name: 'Buying Guide',
        description: 'Comprehensive buying guide with comparisons and recommendations',
        category: 'guide',
        tags: ['guide', 'comparison', 'recommendations'],
        difficulty: 'hard',
        estimatedTime: 90,
        consultationEnabled: true,
        agentCount: 5,
        steps: [
          { id: 'topic-input', name: 'Topic Input', type: 'TEXT_INPUT' },
          { id: 'market-research', name: 'Market Research', type: 'AI_GENERATION' },
          { id: 'keyword-research', name: 'Keyword Research', type: 'AI_GENERATION' },
          { id: 'content-strategy', name: 'Content Strategy', type: 'AI_GENERATION' },
          { id: 'content-generation', name: 'Content Generation', type: 'AI_GENERATION' },
          { id: 'seo-optimization', name: 'SEO Optimization', type: 'AI_GENERATION' },
          { id: 'human-review', name: 'Human Review', type: 'HUMAN_REVIEW' }
        ]
      }
    ];

    return NextResponse.json({
      success: true,
      data: {
        templates
      }
    });
  } catch (error) {
    console.error('Error loading templates:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to load templates' 
      },
      { status: 500 }
    );
  }
}
