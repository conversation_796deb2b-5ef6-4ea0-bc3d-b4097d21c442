import { NextRequest, NextResponse } from 'next/server'
import { v4 as uuidv4 } from 'uuid'
// Temporarily commented out to fix build
// import { stateStore } from '../server-based'
import logger from '../utils/logger'
// import { messageBus, getSessionMessageBus } from '../server-based'
import { IterativeCollaborationState, Event, IterativeMessageType } from '../types'

/**
 * POST handler for receiving user feedback about a session or artifact
 */
export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const { sessionId, artifactId, feedback, toAgent, timestamp } = await req.json()

    if (!sessionId || !feedback) {
      return NextResponse.json(
        { error: 'Missing required parameters: sessionId and feedback' },
        { status: 400 }
      )
    }

    logger.info(`Received feedback for session ${sessionId}`, { 
      sessionId, 
      artifactId: artifactId || 'general',
      feedbackLength: feedback.length,
      toAgent: toAgent || 'any'
    })

    // Try to get state from our enhanced stateStore
    // Temporarily commented out to fix build
    // const state = await stateStore.getState(sessionId)
    const state = null

    // If no state exists, create a new one with just the feedback
    if (!state) {
      logger.warn(`No existing state found for session ${sessionId}, creating basic state with feedback`)
      
      const newState: IterativeCollaborationState = {
        id: sessionId,
        topic: `Feedback Session ${sessionId}`,
        contentType: 'blog-article', // Default content type
        targetAudience: 'general',
        tone: 'neutral',
        keywords: [],
        status: 'active', // Use a valid status from the enum
        startTime: timestamp || new Date().toISOString(),
        artifacts: {},
        consultations: {},
        agentStates: {},
        messages: [],
        iterations: 0,
        maxIterations: 5,
        events: [{
          type: 'SYSTEM',
          timestamp: timestamp || new Date().toISOString(),
          data: {
            eventType: 'USER_FEEDBACK',
            content: feedback,
            artifactId: artifactId || undefined,
            toAgent: toAgent || undefined
          }
        }]
      }
      
      // await stateStore.setState(sessionId, newState)
      
      return NextResponse.json({
        success: true,
        message: 'New feedback session created successfully',
        feedbackId: sessionId // Use sessionId since we don't have a feedback ID
      })
    }
    
    // Create a feedback event
    const feedbackEvent: Event = {
      type: 'SYSTEM',
      timestamp: timestamp || new Date().toISOString(),
      data: {
        eventType: 'USER_FEEDBACK',
        feedback,
        artifactId: artifactId || undefined,
        toAgent: toAgent || undefined
      }
    }
    
    // Add the event to state
    if (!state.events) {
      state.events = [];
    }
    state.events.push(feedbackEvent)
    
    // Store the feedback as a message in the messages array
    if (!state.messages) {
      state.messages = [];
    }
    
    const feedbackMessageId = uuidv4();
    
    // Create a message representation of the feedback
    const feedbackMessage = {
      id: feedbackMessageId,
      timestamp: timestamp || new Date().toISOString(),
      from: 'user',
      to: toAgent || 'system',
      type: IterativeMessageType.FEEDBACK,
      content: {
        feedback,
        artifactId: artifactId || undefined
      },
      conversationId: sessionId
    };
    
    // Add the message to the state
    state.messages.push(feedbackMessage);
    
    // Update the state
    // await stateStore.setState(sessionId, state);

    // Also notify agents if a specific agent was mentioned
    // Temporarily commented out to fix build
    /*
    if (toAgent) {
      try {
        const sessionBus = getSessionMessageBus(sessionId);

        // Create a system message to the specified agent
        const message = sessionBus.createDirectMessage(
          'system',
          toAgent,
          IterativeMessageType.FEEDBACK,
          { feedback, artifactId }
        );

        // Send the message
        await sessionBus.sendMessage(message);
        logger.debug(`Feedback sent as message to agent ${toAgent}`, { sessionId, messageId: message.id });
      } catch (msgError) {
        logger.error(`Error sending feedback message to agent: ${(msgError as Error).message}`);
        // Continue anyway - we've already recorded the feedback in state
      }
    }
    */

    return NextResponse.json({
      success: true,
      message: 'Feedback recorded successfully',
      feedbackId: feedbackMessage.id
    })
  } catch (error) {
    const err = error as Error
    logger.error('Error processing feedback:', {
      error: err.message,
      stack: err.stack
    })
    
    return NextResponse.json(
      {
        error: 'Failed to process feedback',
        details: err.message
      },
      { status: 500 }
    )
  }
}
