/**
 * Simple Approval Flow Explanation
 * Clear, concise explanation of how AI + Human approval works
 */

'use client';

interface ApprovalFlowSimpleExplanationProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ApprovalFlowSimpleExplanation({ isOpen, onClose }: ApprovalFlowSimpleExplanationProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold">How AI + Human Approval Works</h2>
              <p className="text-blue-100 mt-1">Simple explanation of the workflow</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Simple Flow */}
          <div className="space-y-6">
            <div className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg">
              <div className="text-3xl">🤖</div>
              <div>
                <h3 className="font-semibold text-blue-900">1. AI Does the Work</h3>
                <p className="text-blue-700 text-sm">AI generates content automatically (keywords, blog posts, etc.)</p>
              </div>
            </div>

            <div className="flex items-center justify-center">
              <div className="text-2xl">⬇️</div>
            </div>

            <div className="flex items-center space-x-4 p-4 bg-yellow-50 rounded-lg">
              <div className="text-3xl">⏸️</div>
              <div>
                <h3 className="font-semibold text-yellow-900">2. Workflow Pauses</h3>
                <p className="text-yellow-700 text-sm">System stops and waits for human approval</p>
              </div>
            </div>

            <div className="flex items-center justify-center">
              <div className="text-2xl">⬇️</div>
            </div>

            <div className="flex items-center space-x-4 p-4 bg-purple-50 rounded-lg">
              <div className="text-3xl">👤</div>
              <div>
                <h3 className="font-semibold text-purple-900">3. Human Reviews</h3>
                <p className="text-purple-700 text-sm">You review the AI's work and decide: approve or reject</p>
              </div>
            </div>

            <div className="flex items-center justify-center">
              <div className="text-2xl">⬇️</div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg">
                <div className="text-2xl">✅</div>
                <div>
                  <h4 className="font-semibold text-green-900">Approve</h4>
                  <p className="text-green-700 text-xs">Workflow continues</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-4 bg-red-50 rounded-lg">
                <div className="text-2xl">❌</div>
                <div>
                  <h4 className="font-semibold text-red-900">Reject</h4>
                  <p className="text-red-700 text-xs">Workflow stops</p>
                </div>
              </div>
            </div>
          </div>

          {/* Key Points */}
          <div className="mt-8 bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3">🔑 Key Points:</h3>
            <ul className="space-y-2 text-sm text-gray-700">
              <li className="flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                <span><strong>AI generates</strong> all the content automatically</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                <span><strong>Humans approve</strong> the AI's work at key stages</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                <span><strong>Workflow pauses</strong> completely until you make a decision</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                <span><strong>Your decision</strong> determines if the workflow continues or stops</span>
              </li>
            </ul>
          </div>

          {/* Example */}
          <div className="mt-6 bg-blue-50 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">📝 Example: Blog Post Creation</h3>
            <div className="text-sm text-blue-800 space-y-1">
              <p>1. 🤖 AI researches keywords → ⏸️ Pauses for your approval</p>
              <p>2. ✅ You approve keywords → 🤖 AI writes blog post</p>
              <p>3. ⏸️ Pauses for final approval → ✅ You approve → ✨ Done!</p>
            </div>
          </div>

          {/* What You'll See */}
          <div className="mt-6 bg-yellow-50 rounded-lg p-4">
            <h3 className="font-semibold text-yellow-900 mb-2">👀 What You'll See:</h3>
            <div className="text-sm text-yellow-800 space-y-1">
              <p>• <strong>Yellow pulsing circles</strong> when approval is needed</p>
              <p>• <strong>"Workflow Paused"</strong> banner at the top</p>
              <p>• <strong>"Review & Approve"</strong> button to click</p>
              <p>• <strong>Clear approve/reject options</strong> in the approval page</p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 rounded-b-lg">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Got it! Let's try it
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
