/**
 * Inline Approval Component
 * Provides approval interface within the workflow without opening new tabs
 */

'use client';

import { useState, useEffect } from 'react';

interface Artifact {
  id: string;
  type: string;
  title: string;
  content: any;
  status: string;
  stepId: string;
  executionId: string;
  createdAt: string;
  metadata?: Record<string, any>;
}

interface InlineApprovalProps {
  artifactId: string;
  onApprovalComplete?: (artifactId: string, approved: boolean) => void;
  onClose?: () => void;
  currentUser?: string;
}

export default function InlineApproval({
  artifactId,
  onApprovalComplete,
  onClose,
  currentUser = 'demo-user'
}: InlineApprovalProps) {
  const [artifact, setArtifact] = useState<Artifact | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [decision, setDecision] = useState<'approve' | 'reject' | ''>('');
  const [feedback, setFeedback] = useState('');

  useEffect(() => {
    loadArtifact();
  }, [artifactId]);

  const loadArtifact = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/artifact/${artifactId}`);
      if (response.ok) {
        const result = await response.json();
        setArtifact(result.data || result);
      } else {
        setError('Failed to load artifact');
      }
    } catch (err) {
      setError('Error loading artifact');
      console.error('Error loading artifact:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!decision || !artifact) return;

    try {
      setIsSubmitting(true);
      setError(null);

      const response = await fetch('/api/workflow/approval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          artifactId,
          approved: decision === 'approve',
          approver: currentUser,
          feedback,
          reason: decision === 'reject' ? feedback : undefined
        })
      });

      const result = await response.json();

      if (result.success) {
        if (onApprovalComplete) {
          onApprovalComplete(artifactId, decision === 'approve');
        }
        
        // Close the modal after successful submission
        setTimeout(() => {
          onClose?.();
        }, 1000);
      } else {
        setError(result.error || 'Failed to submit approval');
      }
    } catch (err) {
      setError('Error submitting approval');
      console.error('Error submitting approval:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderContent = () => {
    if (!artifact) return null;

    switch (artifact.type) {
      case 'keyword_research':
        return (
          <div className="space-y-4">
            <h4 className="font-medium">Keyword Research Results</h4>
            <div className="bg-gray-50 p-3 rounded">
              <pre className="whitespace-pre-wrap text-sm">
                {typeof artifact.content === 'string' 
                  ? artifact.content 
                  : JSON.stringify(artifact.content, null, 2)}
              </pre>
            </div>
          </div>
        );
      
      case 'blog_post':
      case 'article':
        return (
          <div className="space-y-4">
            <h4 className="font-medium">Article Content</h4>
            <div className="bg-gray-50 p-3 rounded max-h-60 overflow-y-auto">
              <div className="prose prose-sm">
                {typeof artifact.content === 'string' 
                  ? artifact.content.split('\n').map((line, i) => (
                      <p key={i} className="mb-2">{line}</p>
                    ))
                  : JSON.stringify(artifact.content, null, 2)}
              </div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className="space-y-4">
            <h4 className="font-medium">Content</h4>
            <div className="bg-gray-50 p-3 rounded max-h-60 overflow-y-auto">
              <pre className="whitespace-pre-wrap text-sm">
                {typeof artifact.content === 'string' 
                  ? artifact.content 
                  : JSON.stringify(artifact.content, null, 2)}
              </pre>
            </div>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading artifact...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-600 mb-4">❌ {error}</div>
        <button
          onClick={loadArtifact}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!artifact) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Artifact not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Review Artifact</h3>
          <p className="text-sm text-gray-600">{artifact.title}</p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-xs bg-gray-100 px-2 py-1 rounded">{artifact.type}</span>
          <span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">
            {artifact.status}
          </span>
        </div>
      </div>

      {/* Content */}
      {renderContent()}

      {/* Decision Form */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Decision
          </label>
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                name="decision"
                value="approve"
                checked={decision === 'approve'}
                onChange={(e) => setDecision(e.target.value as 'approve')}
                className="mr-2"
              />
              ✅ Approve
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="decision"
                value="reject"
                checked={decision === 'reject'}
                onChange={(e) => setDecision(e.target.value as 'reject')}
                className="mr-2"
              />
              ❌ Reject
            </label>
          </div>
        </div>

        {decision === 'reject' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Feedback (Required for rejection)
            </label>
            <textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Please explain what needs to be improved..."
              className="w-full h-24 p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
        )}

        {error && (
          <div className="text-red-600 text-sm">
            ❌ {error}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            disabled={isSubmitting}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={!decision || isSubmitting || (decision === 'reject' && !feedback.trim())}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Decision'}
          </button>
        </div>
      </div>
    </div>
  );
}
