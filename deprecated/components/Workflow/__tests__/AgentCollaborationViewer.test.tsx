/**
 * Agent Collaboration Viewer Tests
 * 
 * Test-driven development for real-time collaboration monitoring UI
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AgentCollaborationViewer } from '../AgentCollaborationViewer';
import { CollaborationSession, CollaborationResult, CollaborationRound } from '../../../core/agents/AgentCollaborationEngine';

// Mock collaboration data
const mockCollaborationSession: CollaborationSession = {
  id: 'test-session-1',
  task: {
    type: 'artifact-refinement',
    stepId: 'content-creation',
    stepType: 'content-creation',
    objective: 'Create high-quality content through collaboration'
  },
  agents: ['seo-keyword', 'market-research', 'content-strategy'],
  startedAt: '2024-01-15T10:00:00Z',
  status: 'active'
};

const mockCollaborationRounds: CollaborationRound[] = [
  {
    number: 1,
    agentInputs: new Map([
      ['seo-keyword', {
        agentId: 'seo-keyword',
        roundNumber: 1,
        analysis: { keywords: ['AI', 'startups', '2025'] },
        suggestions: ['Use primary keyword in title', 'Add meta description'],
        confidence: 0.85,
        reasoning: 'Strong SEO potential for this topic'
      }],
      ['market-research', {
        agentId: 'market-research',
        roundNumber: 1,
        analysis: { marketSize: 'large', growth: 'high' },
        suggestions: ['Target B2B audience', 'Focus on ROI metrics'],
        confidence: 0.8,
        reasoning: 'Market shows strong demand for AI startup content'
      }]
    ]),
    peerReviews: new Map(),
    synthesizedResult: {
      content: 'Enhanced content with SEO and market insights',
      confidence: 0.825
    }
  },
  {
    number: 2,
    agentInputs: new Map([
      ['content-strategy', {
        agentId: 'content-strategy',
        roundNumber: 2,
        analysis: { structure: 'optimized', flow: 'improved' },
        suggestions: ['Add executive summary', 'Include case studies'],
        confidence: 0.9,
        reasoning: 'Content structure aligns with best practices'
      }]
    ]),
    peerReviews: new Map(),
    synthesizedResult: {
      content: 'Final enhanced content with all agent contributions',
      confidence: 0.88
    }
  }
];

const mockCollaborationResult: CollaborationResult = {
  artifact: {
    id: 'final-artifact',
    type: 'content',
    content: 'Final collaborative content about AI startups',
    metadata: { collaborationComplete: true }
  },
  consensus: {
    confidence: 0.88,
    agreements: ['High-quality content structure', 'Strong SEO optimization'],
    disagreements: [],
    finalRecommendations: ['Use primary keyword in title', 'Target B2B audience', 'Add executive summary'],
    qualityScore: 88
  },
  rounds: mockCollaborationRounds,
  session: mockCollaborationSession
};

describe('AgentCollaborationViewer', () => {
  const mockOnInteraction = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Collaboration Timeline', () => {
    test('should render collaboration timeline with rounds', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
          onInteraction={mockOnInteraction}
        />
      );

      expect(screen.getByText('🤝 Agent Collaboration Progress')).toBeInTheDocument();
      expect(screen.getByText('Round 1')).toBeInTheDocument();
      expect(screen.getByText('Round 2')).toBeInTheDocument();
    });

    test('should show agent participation in each round', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
        />
      );

      // Check for agent avatars/indicators
      expect(screen.getByTestId('round-1-participants')).toBeInTheDocument();
      expect(screen.getByTestId('round-2-participants')).toBeInTheDocument();
    });

    test('should highlight active round', () => {
      render(
        <AgentCollaborationViewer
          collaborationResult={mockCollaborationResult}
          activeRound={1}
        />
      );

      const round2Element = screen.getByTestId('round-2');
      expect(round2Element).toHaveClass('active');
    });

    test('should allow round selection', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
          onRoundSelect={mockOnInteraction}
        />
      );

      const round2Element = screen.getByTestId('round-2');
      fireEvent.click(round2Element);

      expect(mockOnInteraction).toHaveBeenCalledWith({ type: 'round-select', roundNumber: 2 });
    });
  });

  describe('Agent Conversation View', () => {
    test('should display agent inputs for selected round', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
          activeRound={0} // Round 1 (0-indexed)
        />
      );

      expect(screen.getByText('SEO Keyword Agent')).toBeInTheDocument();
      expect(screen.getByText('Market Research Agent')).toBeInTheDocument();
      expect(screen.getByText('Strong SEO potential for this topic')).toBeInTheDocument();
    });

    test('should show agent confidence levels', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
          activeRound={0}
        />
      );

      expect(screen.getByText('85%')).toBeInTheDocument(); // SEO agent confidence
      expect(screen.getByText('80%')).toBeInTheDocument(); // Market research confidence
    });

    test('should display agent suggestions', () => {
      render(
        <AgentCollaborationViewer
          collaborationResult={mockCollaborationResult}
          activeRound={0}
        />
      );

      expect(screen.getAllByText('Use primary keyword in title')).toHaveLength(2); // One in suggestions, one in final recommendations
      expect(screen.getAllByText('Target B2B audience')).toHaveLength(2); // One in suggestions, one in final recommendations
    });

    test('should show empty state when no round selected', () => {
      render(
        <AgentCollaborationViewer
          collaborationResult={mockCollaborationResult}
          activeRound={-1}
        />
      );

      expect(screen.getByText('Collaboration Details')).toBeInTheDocument();
      expect(screen.getByText('Select a round to view collaboration details')).toBeInTheDocument();
    });
  });

  describe('Real-time Updates', () => {
    test('should update when collaboration result changes', async () => {
      const { rerender } = render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
        />
      );

      expect(screen.getByText('Round 2')).toBeInTheDocument();

      // Add a new round
      const updatedResult = {
        ...mockCollaborationResult,
        rounds: [
          ...mockCollaborationRounds,
          {
            number: 3,
            agentInputs: new Map(),
            peerReviews: new Map(),
            synthesizedResult: null
          }
        ]
      };

      rerender(
        <AgentCollaborationViewer 
          collaborationResult={updatedResult}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Round 3')).toBeInTheDocument();
      });
    });

    test('should show loading state for active collaboration', () => {
      const activeResult = {
        ...mockCollaborationResult,
        session: { ...mockCollaborationSession, status: 'active' as const }
      };

      render(
        <AgentCollaborationViewer 
          collaborationResult={activeResult}
          isLive={true}
        />
      );

      expect(screen.getByTestId('collaboration-status')).toHaveTextContent('Active');
      expect(screen.getByTestId('live-indicator')).toBeInTheDocument();
    });
  });

  describe('Human Intervention Panel', () => {
    test('should render intervention options when callback provided', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
          onInteraction={mockOnInteraction}
        />
      );

      expect(screen.getByText('💬 Join the Collaboration')).toBeInTheDocument();
      expect(screen.getByText('Add Requirement')).toBeInTheDocument();
      expect(screen.getByText('Provide Feedback')).toBeInTheDocument();
      expect(screen.getByText('Resolve Conflict')).toBeInTheDocument();
    });

    test('should handle add requirement interaction', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
          onInteraction={mockOnInteraction}
        />
      );

      const addRequirementButton = screen.getByText('Add Requirement');
      fireEvent.click(addRequirementButton);

      expect(mockOnInteraction).toHaveBeenCalledWith({ type: 'add-requirement' });
    });

    test('should handle provide feedback interaction', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
          onInteraction={mockOnInteraction}
        />
      );

      const provideFeedbackButton = screen.getByText('Provide Feedback');
      fireEvent.click(provideFeedbackButton);

      expect(mockOnInteraction).toHaveBeenCalledWith({ type: 'provide-feedback' });
    });

    test('should not render intervention panel when no callback provided', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
        />
      );

      expect(screen.queryByText('💬 Join the Collaboration')).not.toBeInTheDocument();
    });
  });

  describe('Consensus Display', () => {
    test('should show consensus information', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
        />
      );

      expect(screen.getByText('Consensus: 88%')).toBeInTheDocument();
      expect(screen.getByText('Quality Score: 88')).toBeInTheDocument();
    });

    test('should display final recommendations', () => {
      render(
        <AgentCollaborationViewer
          collaborationResult={mockCollaborationResult}
        />
      );

      expect(screen.getByText('Final Recommendations')).toBeInTheDocument();
      expect(screen.getAllByText('Use primary keyword in title')).toHaveLength(2); // One in suggestions, one in final recommendations
      expect(screen.getAllByText('Target B2B audience')).toHaveLength(2); // One in suggestions, one in final recommendations
    });

    test('should show agreements and disagreements', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
        />
      );

      expect(screen.getByText('Agreements')).toBeInTheDocument();
      expect(screen.getByText('High-quality content structure')).toBeInTheDocument();
      expect(screen.getByText('Strong SEO optimization')).toBeInTheDocument();
    });
  });

  describe('Agent Icons and Names', () => {
    test('should display correct agent names', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
          activeRound={0}
        />
      );

      expect(screen.getByText('SEO Keyword Agent')).toBeInTheDocument();
      expect(screen.getByText('Market Research Agent')).toBeInTheDocument();
    });

    test('should show agent icons', () => {
      render(
        <AgentCollaborationViewer 
          collaborationResult={mockCollaborationResult}
          activeRound={0}
        />
      );

      expect(screen.getByTestId('agent-icon-seo-keyword')).toBeInTheDocument();
      expect(screen.getByTestId('agent-icon-market-research')).toBeInTheDocument();
    });
  });
});
