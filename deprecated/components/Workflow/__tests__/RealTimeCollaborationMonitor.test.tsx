/**
 * Real-Time Collaboration Monitor Tests
 *
 * Test-driven development for real-time monitoring of agent collaboration sessions
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { RealTimeCollaborationMonitor } from '../RealTimeCollaborationMonitor';

// Mock WebSocket to prevent actual connections
const mockWebSocket = {
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 1
};

global.WebSocket = jest.fn(() => mockWebSocket) as any;

// Mock collaboration session data
const mockActiveSession = {
  id: 'session-123',
  task: {
    type: 'artifact-refinement' as const,
    stepId: 'content-creation',
    stepType: 'content-creation',
    objective: 'Create high-quality content'
  },
  agents: ['seo-keyword', 'market-research', 'content-strategy'],
  startedAt: '2024-01-15T10:00:00Z',
  status: 'active' as const
};

const mockCollaborationUpdate = {
  sessionId: 'session-123',
  type: 'round-started',
  data: {
    roundNumber: 2,
    participatingAgents: ['seo-keyword', 'content-strategy'],
    timestamp: '2024-01-15T10:05:00Z'
  }
};

describe('RealTimeCollaborationMonitor', () => {
  let mockOnSessionUpdate: jest.Mock;
  let mockOnAgentActivity: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    mockOnSessionUpdate = jest.fn();
    mockOnAgentActivity = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    test('should render monitor component', () => {
      render(
        <RealTimeCollaborationMonitor
          sessionId="session-123"
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      expect(screen.getByText('📡 Real-Time Collaboration Monitor')).toBeInTheDocument();
      expect(screen.getByTestId('connection-status')).toBeInTheDocument();
    });
  });

  describe('Real-time Updates', () => {
    test('should display active collaboration sessions', () => {
      render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          activeSessions={[mockActiveSession]}
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      expect(screen.getByText('Active Collaborations')).toBeInTheDocument();
      expect(screen.getByText('session-123')).toBeInTheDocument();
      expect(screen.getByText('3 agents')).toBeInTheDocument();
    });

    test('should handle incoming collaboration updates', async () => {
      render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      // Simulate incoming WebSocket message
      const messageHandler = (mockWebSocket.addEventListener as jest.Mock).mock.calls
        .find(call => call[0] === 'message')?.[1];
      
      if (messageHandler) {
        act(() => {
          messageHandler({
            data: JSON.stringify(mockCollaborationUpdate)
          });
        });
      }

      await waitFor(() => {
        expect(mockOnSessionUpdate).toHaveBeenCalledWith(mockCollaborationUpdate);
      });
    });

    test('should display agent activity indicators', () => {
      const agentActivities = [
        { agentId: 'seo-keyword', status: 'analyzing', lastSeen: '2024-01-15T10:05:00Z' },
        { agentId: 'market-research', status: 'idle', lastSeen: '2024-01-15T10:04:00Z' }
      ];

      render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          agentActivities={agentActivities}
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      expect(screen.getByText('Agent Activity')).toBeInTheDocument();
      expect(screen.getByTestId('agent-activity-seo-keyword')).toBeInTheDocument();
      expect(screen.getByTestId('agent-activity-market-research')).toBeInTheDocument();
    });

    test('should show collaboration progress', () => {
      const progressData = {
        currentRound: 2,
        totalRounds: 3,
        consensusLevel: 0.75,
        participatingAgents: 3
      };

      render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          progressData={progressData}
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      expect(screen.getByText('Progress')).toBeInTheDocument();
      expect(screen.getByText('Round 2 of 3')).toBeInTheDocument();
      expect(screen.getByText('75%')).toBeInTheDocument(); // Consensus level
    });
  });

  describe('Agent Status Monitoring', () => {
    test('should show agent status indicators', () => {
      const agentActivities = [
        { agentId: 'seo-keyword', status: 'analyzing', lastSeen: '2024-01-15T10:05:00Z' },
        { agentId: 'market-research', status: 'waiting', lastSeen: '2024-01-15T10:04:00Z' },
        { agentId: 'content-strategy', status: 'responding', lastSeen: '2024-01-15T10:05:30Z' }
      ];

      render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          agentActivities={agentActivities}
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      expect(screen.getByTestId('status-analyzing')).toBeInTheDocument();
      expect(screen.getByTestId('status-waiting')).toBeInTheDocument();
      expect(screen.getByTestId('status-responding')).toBeInTheDocument();
    });

    test('should update agent status in real-time', async () => {
      const { rerender } = render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          agentActivities={[
            { agentId: 'seo-keyword', status: 'analyzing', lastSeen: '2024-01-15T10:05:00Z' }
          ]}
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      expect(screen.getByTestId('status-analyzing')).toBeInTheDocument();

      // Update agent status
      rerender(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          agentActivities={[
            { agentId: 'seo-keyword', status: 'completed', lastSeen: '2024-01-15T10:06:00Z' }
          ]}
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status-completed')).toBeInTheDocument();
      });
    });

    test('should handle agent timeout detection', async () => {
      const staleActivity = {
        agentId: 'seo-keyword',
        status: 'analyzing',
        lastSeen: new Date(Date.now() - 60000).toISOString() // 1 minute ago
      };

      render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          agentActivities={[staleActivity]}
          onSessionUpdate={mockOnSessionUpdate}
          agentTimeoutMs={30000} // 30 seconds
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status-timeout')).toBeInTheDocument();
      });
    });
  });

  describe('Performance Metrics', () => {
    test('should display collaboration metrics', () => {
      const metrics = {
        averageResponseTime: 2.5,
        consensusVelocity: 0.15,
        agentParticipation: 0.9,
        qualityScore: 85
      };

      render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          metrics={metrics}
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      expect(screen.getByText('Performance Metrics')).toBeInTheDocument();
      expect(screen.getByText('2.5s')).toBeInTheDocument(); // Average response time
      expect(screen.getByText('85')).toBeInTheDocument(); // Quality score
    });

    test('should update metrics in real-time', async () => {
      const { rerender } = render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          metrics={{ qualityScore: 75 }}
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      expect(screen.getByText('75')).toBeInTheDocument();

      rerender(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          metrics={{ qualityScore: 85 }}
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('85')).toBeInTheDocument();
      });
    });
  });

  describe('Human Intervention Alerts', () => {
    test('should show intervention alerts when needed', () => {
      const alerts = [
        {
          id: 'alert-1',
          type: 'consensus-stalled',
          message: 'Consensus building has stalled at 65%',
          severity: 'warning',
          timestamp: '2024-01-15T10:05:00Z'
        },
        {
          id: 'alert-2',
          type: 'agent-timeout',
          message: 'SEO agent has not responded for 2 minutes',
          severity: 'error',
          timestamp: '2024-01-15T10:06:00Z'
        }
      ];

      render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          alerts={alerts}
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      expect(screen.getByText('Intervention Needed')).toBeInTheDocument();
      expect(screen.getByText('Consensus building has stalled at 65%')).toBeInTheDocument();
      expect(screen.getByText('SEO agent has not responded for 2 minutes')).toBeInTheDocument();
    });

    test('should handle alert dismissal', async () => {
      const mockOnAlertDismiss = jest.fn();
      const alerts = [
        {
          id: 'alert-1',
          type: 'consensus-stalled',
          message: 'Test alert',
          severity: 'warning',
          timestamp: '2024-01-15T10:05:00Z'
        }
      ];

      render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          alerts={alerts}
          onSessionUpdate={mockOnSessionUpdate}
          onAlertDismiss={mockOnAlertDismiss}
        />
      );

      const dismissButton = screen.getByTestId('dismiss-alert-alert-1');
      dismissButton.click();

      expect(mockOnAlertDismiss).toHaveBeenCalledWith('alert-1');
    });
  });

  describe('Cleanup', () => {
    test('should close WebSocket connection on unmount', () => {
      const { unmount } = render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      unmount();

      expect(mockWebSocket.close).toHaveBeenCalled();
    });

    test('should clear reconnection timer on unmount', () => {
      const { unmount } = render(
        <RealTimeCollaborationMonitor 
          sessionId="session-123"
          onSessionUpdate={mockOnSessionUpdate}
        />
      );

      // Simulate disconnect to start reconnection timer
      const closeHandler = (mockWebSocket.addEventListener as jest.Mock).mock.calls
        .find(call => call[0] === 'close')?.[1];
      
      if (closeHandler) {
        act(() => {
          closeHandler(new Event('close'));
        });
      }

      unmount();

      // Should not attempt reconnection after unmount
      expect(WebSocket).toHaveBeenCalledTimes(1);
    });
  });
});
